import time
import threading
import structlog
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from app.utils.redis.redis_service import RedisService
from app.modules.connectors.handlers.github.services.github_service import GitHubService

logger = structlog.get_logger()


class GitHubSyncWorker:
    """
    Background worker for GitHub synchronization operations.
    """

    def __init__(self):
        self.redis_service = RedisService()
        self.github_service = GitHubService()
        self.running = False
        self.worker_thread = None
        self.sync_interval = 3600  # 1 hour default
        
    def start(self):
        """Start the sync worker."""
        if self.running:
            logger.warning("GitHub sync worker is already running")
            return
            
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        logger.info("GitHub sync worker started")
        
    def stop(self):
        """Stop the sync worker."""
        self.running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=10)
        logger.info("GitHub sync worker stopped")
        
    def _worker_loop(self):
        """Main worker loop."""
        while self.running:
            try:
                import time
                import json

                # Get the next job from the sorted set (following GDrive pattern)
                jobs = self.redis_service.zrangebyscore(
                    "github_sync_queue",
                    "-inf",
                    time.time(),
                    start=0,
                    num=1
                )
                if not jobs:
                    # No jobs ready yet, sleep and try again
                    time.sleep(self.poll_interval)
                    continue

                job_id = jobs[0]

                # Remove the job from the queue
                self.redis_service.zrem("github_sync_queue", job_id)

                # Get the job data
                job_data_str = self.redis_service.get(job_id)
                if not job_data_str:
                    logger.warning(f"Job {job_id} not found in Redis")
                    continue

                # Parse job data
                job_data = json.loads(job_data_str)
                job_type = job_data.get('job_type', 'incremental_sync')  # Default to incremental
                organisation_id = job_data.get('organisation_id')

                if not organisation_id:
                    logger.error(f"Invalid job data - missing organisation_id: {job_data}")
                    continue

                # Process the sync job
                self._execute_sync_job(job_data)

                # Delete the job
                self.redis_service.delete(job_id)

            except Exception as e:
                logger.error(f"Error processing GitHub sync job: {str(e)}")
                time.sleep(self.poll_interval)


    def _execute_sync_job(self, job_info: Dict[str, Any]):
        """Execute a sync job."""
        try:
            job_type = job_info.get('job_type')  # Fixed to match GitHub service field name
            organisation_id = job_info.get('organisation_id')

            logger.info(f"Processing GitHub sync job for organization {organisation_id}")

            if job_type == 'full_sync':
                # Perform full sync
                user_id = job_info.get('user_id')
                full_sync = job_info.get('full_sync', True)

                if not user_id:
                    logger.error(f"Invalid full_sync job data - missing user_id: {job_info}")
                    return

                success, message, repos_synced, total_synced = self.github_service.sync_github(
                    organisation_id=organisation_id,
                    full_sync=full_sync
                )

                # Log the result
                if success:
                    logger.info(f"GitHub full sync completed for organization {organisation_id}: {repos_synced} repositories, {total_synced} total items")
                else:
                    logger.error(f"GitHub full sync failed for organization {organisation_id}: {message}")

            elif job_type == 'incremental_sync':
                # Perform incremental sync
                user_id = job_info.get('user_id')
                full_sync = job_info.get('full_sync', False)

                if not user_id:
                    logger.error(f"Invalid incremental_sync job data - missing user_id: {job_info}")
                    return

                success, message, repos_synced, total_synced = self.github_service.sync_github(
                    organisation_id=organisation_id,
                    full_sync=full_sync
                )

                # Log the result
                if success:
                    logger.info(f"GitHub incremental sync completed for organization {organisation_id}: {repos_synced} repositories, {total_synced} total items")
                else:
                    logger.error(f"GitHub incremental sync failed for organization {organisation_id}: {message}")

            elif job_type == 'repository_sync':
                # Sync specific repository
                github_url = job_info.get('github_url')
                agent_id = job_info.get('agent_id')
                user_id = job_info.get('user_id')

                if not github_url:
                    logger.error(f"Invalid repository_sync job data - missing github_url: {job_info}")
                    return

                success, message, items_synced = self.github_service.sync_repository_by_url(
                    github_url=github_url,
                    agent_id=agent_id,
                    user_id=user_id,
                    organisation_id=organisation_id,
                    full_sync=job_info.get('full_sync', False)
                )

                # Log the result
                if success:
                    logger.info(f"GitHub repository sync completed for organization {organisation_id}: {items_synced} items")
                else:
                    logger.error(f"GitHub repository sync failed for organization {organisation_id}: {message}")

            else:
                logger.error(f"Unknown GitHub sync job type: {job_type}")

        except Exception as e:
            logger.error(f"Error executing GitHub sync job: {str(e)}")
            
    def schedule_sync(self, 
                     organisation_id: str, 
                     sync_type: str = 'incremental_sync',
                     delay_minutes: int = 0,
                     **kwargs) -> str:
        """
        Schedule a GitHub sync job.
        
        Args:
            organisation_id: Organisation ID
            sync_type: Type of sync (full_sync, incremental_sync, repository_sync)
            delay_minutes: Delay in minutes before execution
            **kwargs: Additional job parameters
            
        Returns:
            Job ID
        """
        try:
            import json
            import uuid
            
            # Create job ID
            job_id = str(uuid.uuid4())
            job_key = f"github_sync:{job_id}:{organisation_id}:{sync_type}"
            
            # Calculate execution time
            execution_time = datetime.now() + timedelta(minutes=delay_minutes)
            
            # Create job data
            job_data = {
                'id': job_id,
                'type': sync_type,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat(),
                'scheduled_for': execution_time.isoformat(),
                **kwargs
            }
            
            # Store job data
            self.redis_service.set(job_key, json.dumps(job_data), ex=86400)  # Expire in 24 hours
            
            # Add to sync queue
            self.redis_service.zadd("github_sync_queue", {job_key: execution_time.timestamp()})
            
            logger.info(f"Scheduled GitHub sync job: {job_id} for {execution_time}")
            return job_id
            
        except Exception as e:
            logger.error(f"Error scheduling GitHub sync job: {str(e)}")
            raise

    def schedule_repository_sync_job(self, organisation_id: str, repo_ids: list, delay_seconds: int = 0) -> str:
        """
        Schedule a repository sync job following Google Drive patterns.

        Args:
            organisation_id: The ID of the organization
            repo_ids: List of repository IDs to sync
            delay_seconds: Delay before executing the job

        Returns:
            The job ID
        """
        job_data = {
            'job_type': 'sync_repositories_by_ids',
            'organisation_id': organisation_id,
            'repo_ids': repo_ids,
            'scheduled_at': (datetime.utcnow() + timedelta(seconds=delay_seconds)).isoformat()
        }

        # Store in Redis with appropriate expiration
        job_id = f"github_repo_sync:{organisation_id}:{int(time.time())}"
        self.redis_service.set(
            job_id,
            json.dumps(job_data),
            ex=86400  # 24 hour expiration
        )

        # Add to sorted set for processing
        score = time.time() + delay_seconds
        self.redis_service.zadd("github_sync_queue", {job_id: score})

        logger.info(f"Scheduled GitHub repository sync job: {job_id}")
        return job_id

    def cancel_sync(self, job_id: str, organisation_id: str, sync_type: str) -> bool:
        """
        Cancel a scheduled sync job.
        
        Args:
            job_id: Job ID
            organisation_id: Organisation ID
            sync_type: Sync type
            
        Returns:
            True if cancelled successfully
        """
        try:
            job_key = f"github_sync:{job_id}:{organisation_id}:{sync_type}"
            
            # Remove from queue and delete job data
            self.redis_service.zrem("github_sync_queue", job_key)
            self.redis_service.delete(job_key)
            
            logger.info(f"Cancelled GitHub sync job: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling GitHub sync job: {str(e)}")
            return False
            
    def get_scheduled_jobs(self, organisation_id: str) -> list:
        """
        Get scheduled sync jobs for an organisation.
        
        Args:
            organisation_id: Organisation ID
            
        Returns:
            List of scheduled jobs
        """
        try:
            import json
            
            # Get all jobs from queue
            all_jobs = self.redis_service.zrange("github_sync_queue", 0, -1, withscores=True)
            
            # Filter jobs for this organisation
            org_jobs = []
            for job_key, scheduled_time in all_jobs:
                if f":{organisation_id}:" in job_key:
                    job_data = self.redis_service.get(job_key)
                    if job_data:
                        job_info = json.loads(job_data)
                        job_info['scheduled_timestamp'] = scheduled_time
                        org_jobs.append(job_info)
                        
            return org_jobs
            
        except Exception as e:
            logger.error(f"Error getting scheduled GitHub sync jobs: {str(e)}")
            return []
            
    def get_sync_status(self, organisation_id: str) -> Dict[str, Any]:
        """
        Get sync status for an organisation.
        
        Args:
            organisation_id: Organisation ID
            
        Returns:
            Sync status information
        """
        try:
            # Get sync statistics
            stats = self.github_service.get_sync_statistics(organisation_id)
            
            # Get scheduled jobs
            scheduled_jobs = self.get_scheduled_jobs(organisation_id)
            
            return {
                'organisation_id': organisation_id,
                'statistics': stats,
                'scheduled_jobs_count': len(scheduled_jobs),
                'worker_running': self.running,
                'last_check': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting GitHub sync status: {str(e)}")
            return {
                'organisation_id': organisation_id,
                'error': str(e),
                'worker_running': self.running
            }
