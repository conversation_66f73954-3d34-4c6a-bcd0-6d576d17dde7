import json
from datetime import datetime, timedelta
import structlog
from typing import <PERSON><PERSON>, <PERSON>, Dict, Any, Optional
import requests
import time
from requests.adapters import HTTPA<PERSON>pter
from urllib3.util.retry import Retry
import base64

from app.services.neo4j_service import execute_write_query, execute_read_query
from app.utils.redis.redis_service import RedisService
from app.utils.pinecone.pinecone_service import PineconeService
from app.utils.search.hybrid_search_engine import HybridSearchEngine
from app.utils.source_credentials import get_source_credentials
from app.modules.organisation.repository.source import SourceQueries
from app.utils.constants.sources import SourceType
from app.modules.connectors.handlers.github.repository.github_queries import (
    GitHubUserQueries,
    GitHubOrganizationQueries,
    GitHubRepositoryQueries,
    GitHubIssueQueries,
    GitHubPullRequestQueries,
    GitHubCommitQueries,
    GitHubFileQueries,
    GitHubTeamQueries,
    GitHubTagQueries,
    GitHubReleaseQ<PERSON>ies,
    GitHubCommentQueries,
    GitHubReviewQueries,
    GitHubRelationshipQueries,
    GitHubSyncQueries
)


logger = structlog.get_logger()


class GitHubAPIClient:
    """
    Dedicated GitHub API client for handling all API interactions.
    Provides clean methods for all GitHub API endpoints with proper error handling,
    rate limiting, and retry logic.
    """
    
    def __init__(self, token: str):
        self.token = token
        self.session = self._create_robust_session()
        self.rate_limit_remaining = 5000
        self.rate_limit_reset_time = None
        self.max_retries = 3
        self.retry_delay = 1
        
    def _create_robust_session(self) -> requests.Session:
        """Create a robust session with retry strategy and rate limiting."""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=self.max_retries,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "OPTIONS"],
            backoff_factor=2
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set headers
        session.headers.update({
            'Authorization': f'token {self.token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'RuhOrg-GitHub-Connector/1.0',
            'X-GitHub-Api-Version': '2022-11-28'
        })
        
        return session
        
    def _make_paginated_request(self, url: str, params: Dict = None, limit: int = None) -> List[Dict]:
        """Make paginated API requests and return all results."""
        results = []
        page = 1
        per_page = min(100, limit) if limit else 100
        
        while True:
            request_params = params.copy() if params else {}
            request_params.update({'per_page': per_page, 'page': page})
            
            response = self._make_api_request_with_retry(url, request_params)
            if not response or response.status_code != 200:
                break
                
            data = response.json()
            if not data:
                break
                
            results.extend(data)
            
            # Check if we've reached the limit or if there are no more pages
            if limit and len(results) >= limit:
                results = results[:limit]
                break
                
            if len(data) < per_page:
                break
                
            page += 1
            
        return results
        
    def _make_api_request_with_retry(self, url: str, params: Dict = None, method: str = 'GET') -> Optional[requests.Response]:
        """Make API request with comprehensive error handling and retry logic."""
        for attempt in range(self.max_retries + 1):
            try:
                self._check_and_handle_rate_limits()
                
                if method.upper() == 'GET':
                    response = self.session.get(url, params=params, timeout=30)
                elif method.upper() == 'POST':
                    response = self.session.post(url, json=params, timeout=30)
                else:
                    response = self.session.request(method, url, params=params, timeout=30)
                
                self._update_rate_limit_info(response.headers)
                
                if response.status_code == 200:
                    return response
                elif response.status_code == 404:
                    logger.warning(f"Resource not found: {url}")
                    return None
                elif response.status_code == 403:
                    if 'rate limit' in response.text.lower():
                        logger.warning("Rate limit exceeded, waiting...")
                        self._handle_rate_limit_exceeded(response.headers)
                        continue
                    else:
                        logger.error(f"Access forbidden: {url}")
                        return None
                elif response.status_code == 401:
                    logger.error(f"Authentication failed: {url}")
                    return None
                elif response.status_code >= 500:
                    logger.warning(f"Server error {response.status_code}, retrying... (attempt {attempt + 1})")
                    if attempt < self.max_retries:
                        time.sleep(self.retry_delay * (2 ** attempt))
                        continue
                    else:
                        logger.error(f"Server error persisted after {self.max_retries} retries")
                        return None
                else:
                    logger.warning(f"Unexpected status code {response.status_code}: {url}")
                    return None
                    
            except requests.exceptions.Timeout:
                logger.warning(f"Request timeout for {url}, retrying... (attempt {attempt + 1})")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (2 ** attempt))
                    continue
                else:
                    logger.error(f"Request timeout persisted after {self.max_retries} retries")
                    return None
                    
            except requests.exceptions.ConnectionError:
                logger.warning(f"Connection error for {url}, retrying... (attempt {attempt + 1})")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (2 ** attempt))
                    continue
                else:
                    logger.error(f"Connection error persisted after {self.max_retries} retries")
                    return None
                    
            except Exception as e:
                logger.error(f"Unexpected error making request to {url}: {str(e)}")
                return None
        
        return None
        
    def _check_and_handle_rate_limits(self):
        """Check rate limits and wait if necessary."""
        if self.rate_limit_remaining <= 10:
            if self.rate_limit_reset_time and datetime.now() < self.rate_limit_reset_time:
                wait_time = (self.rate_limit_reset_time - datetime.now()).total_seconds()
                logger.warning(f"Rate limit low ({self.rate_limit_remaining}), waiting {wait_time:.1f} seconds")
                time.sleep(wait_time + 1)
                
                # Refresh rate limit info
                try:
                    rate_limit_response = self.session.get("https://api.github.com/rate_limit", timeout=10)
                    if rate_limit_response.status_code == 200:
                        rate_data = rate_limit_response.json()
                        self.rate_limit_remaining = rate_data.get('resources', {}).get('core', {}).get('remaining', 5000)
                        reset_timestamp = rate_data.get('resources', {}).get('core', {}).get('reset', 0)
                        self.rate_limit_reset_time = datetime.fromtimestamp(reset_timestamp)
                except Exception as e:
                    logger.warning(f"Failed to refresh rate limit info: {str(e)}")

    def _update_rate_limit_info(self, headers: Dict[str, str]):
        """Update rate limit information from response headers."""
        try:
            if 'X-RateLimit-Remaining' in headers:
                self.rate_limit_remaining = int(headers['X-RateLimit-Remaining'])
            if 'X-RateLimit-Reset' in headers:
                reset_timestamp = int(headers['X-RateLimit-Reset'])
                self.rate_limit_reset_time = datetime.fromtimestamp(reset_timestamp)
                
            logger.debug(f"Rate limit - Remaining: {self.rate_limit_remaining}, Reset: {self.rate_limit_reset_time}")
        except (ValueError, KeyError) as e:
            logger.debug(f"Could not parse rate limit headers: {str(e)}")

    def _handle_rate_limit_exceeded(self, headers: Dict[str, str]):
        """Handle rate limit exceeded scenario."""
        try:
            if 'X-RateLimit-Reset' in headers:
                reset_timestamp = int(headers['X-RateLimit-Reset'])
                reset_time = datetime.fromtimestamp(reset_timestamp)
                wait_time = (reset_time - datetime.now()).total_seconds()
                
                if wait_time > 0:
                    logger.warning(f"Rate limit exceeded, waiting {wait_time:.1f} seconds until reset")
                    time.sleep(wait_time + 5)  # Add 5 seconds buffer
                    self.rate_limit_remaining = 5000  # Reset to default
                    self.rate_limit_reset_time = reset_time
        except Exception as e:
            logger.warning(f"Error handling rate limit: {str(e)}, waiting 60 seconds")
            time.sleep(60)

    def _safe_api_call(self, url: str, params: Dict = None, method: str = 'GET', context: str = "API call") -> Optional[Dict]:
        """Safely make API call with comprehensive error handling."""
        try:
            response = self._make_api_request_with_retry(url, params, method)
            if response and response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Failed {context}: {url}")
                return None
        except Exception as e:
            logger.error(f"Error in {context}: {str(e)}")
            return None
            
    def test_authentication(self) -> Optional[Dict]:
        """Test GitHub authentication."""
        return self._safe_api_call("https://api.github.com/user", context="authentication test")
        
    # User API methods
    def get_authenticated_user(self) -> Optional[Dict]:
        """Get the authenticated user's profile."""
        return self._safe_api_call("https://api.github.com/user", context="get authenticated user")
        
    def get_user_organizations(self) -> List[Dict]:
        """Get organizations for the authenticated user."""
        return self._make_paginated_request("https://api.github.com/user/orgs")
        
    def get_user_repositories(self, limit: int = None) -> List[Dict]:
        """Get repositories for the authenticated user."""
        params = {'type': 'all', 'sort': 'updated'}
        return self._make_paginated_request("https://api.github.com/user/repos", params, limit)

    # Organization API methods
    def get_organization(self, org_login: str) -> Optional[Dict]:
        """Get organization details."""
        return self._safe_api_call(f"https://api.github.com/orgs/{org_login}", context=f"get organization {org_login}")

    def get_organization_members(self, org_login: str, limit: int = None) -> List[Dict]:
        """Get all members of an organization."""
        return self._make_paginated_request(f"https://api.github.com/orgs/{org_login}/members", limit=limit)

    def get_organization_teams(self, org_login: str, limit: int = None) -> List[Dict]:
        """Get all teams of an organization."""
        return self._make_paginated_request(f"https://api.github.com/orgs/{org_login}/teams", limit=limit)

    def get_organization_repositories(self, org_login: str, limit: int = None) -> List[Dict]:
        """Get all repositories of an organization."""
        params = {'type': 'all', 'sort': 'updated'}
        return self._make_paginated_request(f"https://api.github.com/orgs/{org_login}/repos", params, limit)

    def get_team_members(self, org_login: str, team_slug: str, limit: int = None) -> List[Dict]:
        """Get members of a specific team."""
        return self._make_paginated_request(f"https://api.github.com/orgs/{org_login}/teams/{team_slug}/members", limit=limit)

    def get_team_repositories(self, org_login: str, team_slug: str, limit: int = None) -> List[Dict]:
        """Get repositories accessible by a specific team."""
        return self._make_paginated_request(f"https://api.github.com/orgs/{org_login}/teams/{team_slug}/repos", limit=limit)

    # Repository API methods
    def get_repository(self, owner: str, repo: str) -> Optional[Dict]:
        """Get repository details."""
        return self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}", context=f"get repository {owner}/{repo}")

    def get_repository_issues(self, owner: str, repo: str, state: str = 'all', limit: int = 100) -> List[Dict]:
        """Get repository issues."""
        params = {'state': state}
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/issues", params, limit)

    def get_repository_pull_requests(self, owner: str, repo: str, state: str = 'all', limit: int = 100) -> List[Dict]:
        """Get repository pull requests."""
        params = {'state': state}
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/pulls", params, limit)

    def get_repository_commits(self, owner: str, repo: str, limit: int = 50) -> List[Dict]:
        """Get repository commits."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/commits", limit=limit)

    def get_repository_branches(self, owner: str, repo: str, limit: int = None) -> List[Dict]:
        """Get repository branches."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/branches", limit=limit)

    def get_repository_tags(self, owner: str, repo: str, limit: int = None) -> List[Dict]:
        """Get repository tags."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/tags", limit=limit)

    def get_repository_releases(self, owner: str, repo: str, limit: int = None) -> List[Dict]:
        """Get repository releases."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/releases", limit=limit)

    def get_repository_contents(self, owner: str, repo: str, path: str = "") -> List[Dict]:
        """Get repository file structure."""
        result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/contents/{path}",
                                   context=f"get repository {owner}/{repo} contents")
        return result if isinstance(result, list) else [result] if result else []

    def get_file_content(self, owner: str, repo: str, path: str) -> Optional[str]:
        """Get file content from repository."""
        result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/contents/{path}",
                                   context=f"get file content {owner}/{repo}/{path}")
        if result and result.get('content'):
            try:
                return base64.b64decode(result['content']).decode('utf-8')
            except Exception as e:
                logger.warning(f"Failed to decode file content: {str(e)}")
                return None
        return None

    def get_issue_comments(self, owner: str, repo: str, issue_number: int, limit: int = None) -> List[Dict]:
        """Get comments for a specific issue."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/issues/{issue_number}/comments", limit=limit)

    def get_pr_reviews(self, owner: str, repo: str, pr_number: int, limit: int = None) -> List[Dict]:
        """Get reviews for a specific pull request."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}/reviews", limit=limit)

    def get_pr_review_comments(self, owner: str, repo: str, pr_number: int, limit: int = None) -> List[Dict]:
        """Get review comments for a specific pull request."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}/comments", limit=limit)


class GitHubEntityMapper:
    """
    Maps raw GitHub API responses to standardized schema entities.
    Handles data normalization, property mapping, and ensures all entities
    conform to the github_schema.yml definitions.
    """

    def __init__(self):
        pass

    def map_user(self, user_data: Dict, organisation_id: str) -> Dict:
        """
        Map GitHub user data to schema format.

        Args:
            user_data: GitHub user data from API
            organisation_id: Main system organization ID (for scoping)
        """
        return {
            'github_user_id': user_data.get('id'),  # GitHub user ID
            'organisation_id': organisation_id,  # Main system organization ID
            'login': user_data.get('login'),
            'name': user_data.get('name'),
            'email': user_data.get('email'),
            'html_url': user_data.get('html_url'),
            'type': user_data.get('type', 'User'),
            'created_at': user_data.get('created_at'),
            'updated_at': user_data.get('updated_at')
        }

    def map_organization(self, org_data: Dict, organisation_id: str) -> Dict:
        """
        Map GitHub organization data to schema format.

        Args:
            org_data: GitHub organization data from API
            organisation_id: Main system organization ID (not GitHub org ID)
        """
        return {
            'github_org_id': org_data.get('id'),  # This is the GitHub organization ID
            'organisation_id': organisation_id,  # This is the main system organization ID
            'login': org_data.get('login'),
            'name': org_data.get('name'),
            'description': org_data.get('description'),
            'html_url': org_data.get('html_url'),
            'public_repos': org_data.get('public_repos', 0),
            'public_gists': org_data.get('public_gists', 0),
            'followers': org_data.get('followers', 0),
            'following': org_data.get('following', 0),
            'created_at': org_data.get('created_at'),
            'updated_at': org_data.get('updated_at')
        }

    def map_repository(self, repo_data: Dict, organisation_id: str) -> Dict:
        """
        Map GitHub repository data to schema format.

        Args:
            repo_data: GitHub repository data from API
            organisation_id: Main system organization ID (for scoping)
        """
        return {
            'repository_id': repo_data.get('id'),  # GitHub repository ID
            'organisation_id': organisation_id,  # Main system organization ID
            'name': repo_data.get('name'),
            'full_name': repo_data.get('full_name'),
            'description': repo_data.get('description'),
            'private': repo_data.get('private', False),
            'html_url': repo_data.get('html_url'),
            'default_branch': repo_data.get('default_branch'),
            'stargazers_count': repo_data.get('stargazers_count', 0),
            'watchers_count': repo_data.get('watchers_count', 0),
            'forks_count': repo_data.get('forks_count', 0),
            'open_issues_count': repo_data.get('open_issues_count', 0),
            'archived': repo_data.get('archived', False),
            'created_at': repo_data.get('created_at'),
            'updated_at': repo_data.get('updated_at')
        }

    def map_issue(self, issue_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub issue data to schema format."""
        return {
            'issue_id': issue_data.get('id'),
            'organisation_id': organisation_id,
            'repository_id': repository_id,
            'number': issue_data.get('number'),
            'title': issue_data.get('title'),
            'body': issue_data.get('body'),
            'state': issue_data.get('state'),
            'html_url': issue_data.get('html_url'),
            'created_at': issue_data.get('created_at'),
            'updated_at': issue_data.get('updated_at'),
            'closed_at': issue_data.get('closed_at')
        }

    def map_pull_request(self, pr_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub pull request data to schema format."""
        return {
            'pr_id': pr_data.get('id'),
            'organisation_id': organisation_id,
            'repository_id': repository_id,
            'number': pr_data.get('number'),
            'title': pr_data.get('title'),
            'body': pr_data.get('body'),
            'state': pr_data.get('state'),
            'html_url': pr_data.get('html_url'),
            'head_branch': pr_data.get('head', {}).get('ref'),
            'base_branch': pr_data.get('base', {}).get('ref'),
            'merged': pr_data.get('merged', False),
            'created_at': pr_data.get('created_at'),
            'updated_at': pr_data.get('updated_at'),
            'merged_at': pr_data.get('merged_at')
        }

    def map_commit(self, commit_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub commit data to schema format."""
        commit_info = commit_data.get('commit', {})
        author_info = commit_info.get('author', {})
        committer_info = commit_info.get('committer', {})

        return {
            'sha': commit_data.get('sha'),
            'organisation_id': organisation_id,
            'repository_id': repository_id,
            'message': commit_info.get('message'),
            'author_name': author_info.get('name'),
            'author_email': author_info.get('email'),
            'committer_name': committer_info.get('name'),
            'committer_email': committer_info.get('email'),
            'html_url': commit_data.get('html_url'),
            'date': author_info.get('date'),
            'created_at': author_info.get('date')
        }

    def map_team(self, team_data: Dict, organisation_id: str, github_organization_id: int) -> Dict:
        """
        Map GitHub team data to schema format.

        Args:
            team_data: GitHub team data from API
            organisation_id: Main system organization ID (for scoping)
            github_organization_id: GitHub organization ID (for GitHub-specific relationships)
        """
        return {
            'team_id': team_data.get('id'),  # GitHub team ID
            'organisation_id': organisation_id,  # Main system organization ID
            'github_organization_id': github_organization_id,  # GitHub organization ID
            'name': team_data.get('name'),
            'slug': team_data.get('slug'),
            'description': team_data.get('description'),
            'privacy': team_data.get('privacy'),
            'permission': team_data.get('permission'),
            'html_url': team_data.get('html_url'),
            'created_at': team_data.get('created_at'),
            'updated_at': team_data.get('updated_at')
        }


class GitHubSyncService:
    """
    Orchestrates the complete GitHub data fetching and storage process.
    Provides clean separation of concerns with methods for organization-level
    and user-level synchronization following Google Drive patterns.
    """

    def __init__(self, api_client: GitHubAPIClient, entity_mapper: GitHubEntityMapper):
        self.api_client = api_client
        self.entity_mapper = entity_mapper

        # Initialize query instances (following Google Drive pattern)
        self.user_queries = GitHubUserQueries()
        self.organization_queries = GitHubOrganizationQueries()
        self.repository_queries = GitHubRepositoryQueries()
        self.issue_queries = GitHubIssueQueries()
        self.pull_request_queries = GitHubPullRequestQueries()
        self.commit_queries = GitHubCommitQueries()
        self.file_queries = GitHubFileQueries()
        self.team_queries = GitHubTeamQueries()
        self.tag_queries = GitHubTagQueries()
        self.release_queries = GitHubReleaseQueries()
        self.comment_queries = GitHubCommentQueries()
        self.review_queries = GitHubReviewQueries()
        self.relationship_queries = GitHubRelationshipQueries()
        self.sync_queries = GitHubSyncQueries()

    def sync_organization(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Sync GitHub organization data following Google Drive patterns.

        Args:
            organisation_id: The organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            logger.info(f"Starting GitHub organization sync for organisation: {organisation_id}")

            # Test authentication
            auth_test = self.api_client.test_authentication()
            if not auth_test:
                return False, "GitHub authentication failed", 0

            logger.info(f"Authenticated as GitHub user: {auth_test.get('login', 'unknown')}")

            items_synced = 0

            # 1. Fetch and sync user organizations
            organizations = self.api_client.get_user_organizations()
            for org_data in organizations:
                # Create organization node
                org_entity = self.entity_mapper.map_organization(org_data, organisation_id)
                self._create_or_update_entity(
                    self.organization_queries.CREATE_OR_UPDATE_GITHUB_ORGANIZATION,
                    org_entity,
                )
                items_synced += 1

                # Sync organization details
                org_details = self.api_client.get_organization(org_data['login'])
                if org_details:
                    org_entity = self.entity_mapper.map_organization(org_details, organisation_id)
                    self._create_or_update_entity(self.organization_queries.CREATE_OR_UPDATE_ORGANIZATION, org_entity)

                # 2. Sync organization members
                members = self.api_client.get_organization_members(org_data['login'])
                for member_data in members:
                    user_entity = self.entity_mapper.map_user(member_data, organisation_id)
                    self._create_or_update_entity(self.user_queries.CREATE_OR_UPDATE_USER, user_entity)
                    self._create_relationship(self.organization_queries.ADD_MEMBER_TO_ORGANIZATION,
                                           {'login': user_entity['login'], 'github_org_id': org_entity['github_org_id']})
                    items_synced += 1

                # 3. Sync organization teams
                teams = self.api_client.get_organization_teams(org_data['login'])
                for team_data in teams:
                    team_entity = self.entity_mapper.map_team(team_data, organisation_id, org_entity['github_org_id'])
                    self._create_or_update_entity(self.team_queries.CREATE_OR_UPDATE_TEAM, team_entity)
                    items_synced += 1

                    # Sync team members
                    team_members = self.api_client.get_team_members(org_data['login'], team_data['slug'])
                    for member_data in team_members:
                        user_entity = self.entity_mapper.map_user(member_data, organisation_id)
                        self._create_or_update_entity(self.user_queries.CREATE_OR_UPDATE_USER, user_entity)
                        self._create_relationship(self.relationship_queries.CREATE_TEAM_MEMBER_RELATIONSHIP,
                                               {'user_id': user_entity['id'], 'team_id': team_entity['id']})
                        items_synced += 1

                # 4. Sync organization repositories
                repositories = self.api_client.get_organization_repositories(org_data['login'])
                for repo_data in repositories:
                    repo_items = self._sync_repository_comprehensive(repo_data, organisation_id, full_sync)
                    items_synced += repo_items

            logger.info(f"Successfully completed GitHub organization sync: {items_synced} items")
            return True, f"Successfully synced GitHub organizations", items_synced

        except Exception as e:
            logger.error(f"Error in GitHub organization sync: {str(e)}")
            return False, f"Organization sync failed: {str(e)}", 0

    def sync_user(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Sync GitHub user data following Google Drive patterns.

        Args:
            organisation_id: The organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            logger.info(f"Starting GitHub user sync for organisation: {organisation_id}")

            # Test authentication
            auth_test = self.api_client.test_authentication()
            if not auth_test:
                return False, "GitHub authentication failed", 0

            logger.info(f"Authenticated as GitHub user: {auth_test.get('login', 'unknown')}")

            items_synced = 0

            # 1. Sync authenticated user profile
            user_data = self.api_client.get_authenticated_user()
            if user_data:
                user_entity = self.entity_mapper.map_user(user_data, organisation_id)
                self._create_or_update_entity(self.user_queries.CREATE_OR_UPDATE_USER, user_entity)
                items_synced += 1

            # 2. Sync user repositories
            repositories = self.api_client.get_user_repositories()
            for repo_data in repositories:
                repo_items = self._sync_repository_comprehensive(repo_data, organisation_id, full_sync)
                items_synced += repo_items

            # 3. Sync user organizations (if any)
            organizations = self.api_client.get_user_organizations()
            for org_data in organizations:
                org_entity = self.entity_mapper.map_organization(org_data, organisation_id)
                self._create_or_update_entity(self.organization_queries.CREATE_OR_UPDATE_ORGANIZATION, org_entity)

                if user_data:
                    self._create_relationship(self.organization_queries.ADD_MEMBER_TO_ORGANIZATION,
                                           {'login': user_entity['login'], 'github_org_id': org_entity['github_org_id']})
                items_synced += 1

            logger.info(f"Successfully completed GitHub user sync: {items_synced} items")
            return True, f"Successfully synced GitHub user data", items_synced

        except Exception as e:
            logger.error(f"Error in GitHub user sync: {str(e)}")
            return False, f"User sync failed: {str(e)}", 0

    def sync_repository(self, repo_data: Dict, organisation_id: str, full_sync: bool = False) -> int:
        """
        Sync a single repository with all its data.

        Args:
            repo_data: Repository data from GitHub API
            organisation_id: The organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Number of items synced
        """
        return self._sync_repository_comprehensive(repo_data, organisation_id, full_sync)

    def _sync_repository_comprehensive(self, repo_data: Dict, organisation_id: str, full_sync: bool = False) -> int:
        """Comprehensive repository sync following Google Drive patterns."""
        try:
            items_synced = 0
            owner = repo_data.get('owner', {}).get('login')
            repo_name = repo_data.get('name')

            if not owner or not repo_name:
                logger.warning(f"Invalid repository data: missing owner or name")
                return 0

            logger.info(f"Syncing repository: {owner}/{repo_name}")

            # 1. Create repository node
            repo_entity = self.entity_mapper.map_repository(repo_data, organisation_id)
            self._create_or_update_entity(self.repository_queries.CREATE_OR_UPDATE_REPOSITORY, repo_entity)
            items_synced += 1

            # 2. Sync repository owner
            if repo_data.get('owner'):
                owner_entity = self.entity_mapper.map_user(repo_data['owner'], organisation_id)
                self._create_or_update_entity(self.user_queries.CREATE_OR_UPDATE_USER, owner_entity)
                self._create_relationship(self.relationship_queries.CREATE_REPOSITORY_OWNERSHIP_RELATIONSHIP,
                                       {'user_id': owner_entity['github_user_id'], 'repository_id': repo_entity['repository_id']})
                items_synced += 1

            # 3. Sync branches
            branches = self.api_client.get_repository_branches(owner, repo_name)
            for branch_data in branches:
                branch_entity = {
                    'name': branch_data.get('name'),
                    'repository_id': repo_entity['repository_id'],
                    'organisation_id': organisation_id,
                    'last_commit_sha': branch_data.get('commit', {}).get('sha'),
                    'protected': branch_data.get('protected', False),
                    'default': branch_data['name'] == repo_data.get('default_branch'),
                    'created_at': datetime.utcnow().isoformat(),
                    'updated_at': datetime.utcnow().isoformat()
                }
                self._create_or_update_entity(self.repository_queries.CREATE_OR_UPDATE_BRANCH, branch_entity)
                items_synced += 1

            if full_sync:
                # 4. Sync issues
                issues = self.api_client.get_repository_issues(owner, repo_name, limit=200)
                for issue_data in issues:
                    # Skip pull requests (they appear in issues API)
                    if issue_data.get('pull_request'):
                        continue

                    issue_entity = self.entity_mapper.map_issue(issue_data, organisation_id, repo_entity['repository_id'])
                    self._create_or_update_entity(self.issue_queries.CREATE_OR_UPDATE_ISSUE, issue_entity)
                    items_synced += 1

                    # Sync issue comments
                    comments = self.api_client.get_issue_comments(owner, repo_name, issue_data['number'])
                    for comment_data in comments:
                        self._create_comment(comment_data, organisation_id, issue_id=issue_entity['issue_id'])
                        items_synced += 1

                # 5. Sync pull requests
                pull_requests = self.api_client.get_repository_pull_requests(owner, repo_name, limit=200)
                for pr_data in pull_requests:
                    pr_entity = self.entity_mapper.map_pull_request(pr_data, organisation_id, repo_entity['repository_id'])
                    self._create_or_update_entity(self.pull_request_queries.CREATE_OR_UPDATE_PULL_REQUEST, pr_entity)
                    items_synced += 1

                    # Sync PR reviews
                    reviews = self.api_client.get_pr_reviews(owner, repo_name, pr_data['number'])
                    for review_data in reviews:
                        self._create_review(review_data, organisation_id, pr_entity['pr_id'])
                        items_synced += 1

                # 6. Sync commits
                commits = self.api_client.get_repository_commits(owner, repo_name, limit=100)
                for commit_data in commits:
                    commit_entity = self.entity_mapper.map_commit(commit_data, organisation_id, repo_entity['repository_id'])
                    self._create_or_update_entity(self.commit_queries.CREATE_OR_UPDATE_COMMIT, commit_entity)
                    items_synced += 1

            # 7. Create organizational relationships
            self._create_organizational_relationships(repo_entity['repository_id'], organisation_id)

            logger.info(f"Successfully synced repository {owner}/{repo_name}: {items_synced} items")
            return items_synced

        except Exception as e:
            logger.error(f"Error syncing repository {owner}/{repo_name}: {str(e)}")
            return 0

    # Helper methods for creating and updating entities
    def _create_or_update_entity(self, query: str, entity_data: Dict):
        """Generic method to create or update any entity."""
        try:
            execute_write_query(query, entity_data)
            logger.debug(f"Created/updated entity: {entity_data.get('id', entity_data.get('name', 'unknown'))}")
        except Exception as e:
            logger.error(f"Error creating/updating entity: {str(e)}")

    def _create_relationship(self, query: str, relationship_data: Dict):
        """Generic method to create relationships."""
        try:
            relationship_data['created_at'] = datetime.utcnow().isoformat()
            execute_write_query(query, relationship_data)
            logger.debug(f"Created relationship: {relationship_data}")
        except Exception as e:
            logger.error(f"Error creating relationship: {str(e)}")

    def _create_comment(self, comment_data: Dict, organisation_id: str, issue_id: int = None, pull_request_id: int = None):
        """Create or update GitHub comment."""
        try:
            params = {
                'id': comment_data.get('id'),
                'organisation_id': organisation_id,
                'body': comment_data.get('body'),
                'html_url': comment_data.get('html_url'),
                'created_at': comment_data.get('created_at'),
                'updated_at': comment_data.get('updated_at')
            }

            if issue_id:
                params['issue_id'] = issue_id
            if pull_request_id:
                params['pull_request_id'] = pull_request_id

            execute_write_query(self.comment_queries.CREATE_OR_UPDATE_COMMENT, params)
            logger.debug(f"Created/updated GitHub comment: {comment_data.get('id')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub comment: {str(e)}")

    def _create_review(self, review_data: Dict, organisation_id: str, pull_request_id: int):
        """Create or update GitHub review."""
        try:
            params = {
                'id': review_data.get('id'),
                'organisation_id': organisation_id,
                'pull_request_id': pull_request_id,
                'state': review_data.get('state'),
                'body': review_data.get('body'),
                'html_url': review_data.get('html_url'),
                'submitted_at': review_data.get('submitted_at')
            }

            execute_write_query(self.review_queries.CREATE_OR_UPDATE_REVIEW, params)
            logger.debug(f"Created/updated GitHub review: {review_data.get('id')}")
        except Exception as e:
            logger.error(f"Error creating/updating GitHub review: {str(e)}")

    def _create_organizational_relationships(self, repository_id: int, organisation_id: str):
        """Create organizational relationships following Google Drive patterns."""
        try:
            # Create organization-repository relationships (mirroring GDrive's organization access)
            params = {
                'organisation_id': organisation_id,
                'repository_id': repository_id,
                'granted_at': datetime.utcnow().isoformat()
            }
            execute_write_query(self.relationship_queries.CREATE_ORGANISATION_REPOSITORY_RELATIONSHIP, params)

            # Create department-repository relationships for all departments (mirroring GDrive's department access)
            execute_write_query(self.relationship_queries.CREATE_DEPARTMENT_REPOSITORY_RELATIONSHIP, params)

            logger.debug(f"Created organizational relationships for repository: {repository_id}")
        except Exception as e:
            logger.error(f"Error creating organizational relationships: {str(e)}")


class GitHubService:
    """
    Refactored GitHub service using modular components.
    Provides clean integration with the existing connector framework
    while using the new GitHubAPIClient, GitHubEntityMapper, and GitHubSyncService.
    """

    def __init__(self):
        self.redis_service = RedisService()
        self.pinecone_service = PineconeService()

        # Initialize modular components
        self.api_client = None  # Will be initialized when credentials are available
        self.entity_mapper = GitHubEntityMapper()
        self.sync_service = None  # Will be initialized when API client is ready

        # Initialize query instances for backward compatibility
        self.user_queries = GitHubUserQueries()
        self.organization_queries = GitHubOrganizationQueries()
        self.repository_queries = GitHubRepositoryQueries()
        self.issue_queries = GitHubIssueQueries()
        self.pull_request_queries = GitHubPullRequestQueries()
        self.commit_queries = GitHubCommitQueries()
        self.file_queries = GitHubFileQueries()
        self.team_queries = GitHubTeamQueries()
        self.tag_queries = GitHubTagQueries()
        self.release_queries = GitHubReleaseQueries()
        self.comment_queries = GitHubCommentQueries()
        self.review_queries = GitHubReviewQueries()
        self.relationship_queries = GitHubRelationshipQueries()
        self.sync_queries = GitHubSyncQueries()

    def _initialize_api_client(self, organisation_id: str) -> bool:
        """Initialize API client with credentials."""
        try:
            credentials = get_source_credentials(organisation_id, 'github')
            if not credentials:
                logger.error(f"No GitHub credentials found for organisation {organisation_id}")
                return False

            token = credentials.get('token') or credentials.get('access_token')
            if not token:
                logger.error("GitHub token not found in credentials")
                return False

            self.api_client = GitHubAPIClient(token)
            self.sync_service = GitHubSyncService(self.api_client, self.entity_mapper)
            return True

        except Exception as e:
            logger.error(f"Error initializing GitHub API client: {str(e)}")
            return False

    def _schedule_sync(self, user_id: str, organisation_id: str = None, full_sync: bool = False, delay_seconds: int = 0) -> str:
        """
        Schedule a GitHub sync job following Google Drive patterns.

        Args:
            user_id: The ID of the user
            organisation_id: The ID of the organisation (required)
            full_sync: Whether to perform a full sync
            delay_seconds: Delay before executing the job

        Returns:
            The job ID

        Raises:
            ValueError: If organisation_id is None
        """
        # organisation_id is required for sync jobs
        if organisation_id is None:
            raise ValueError("organisation_id is required for scheduling sync jobs")

        job_data = {
            'user_id': user_id,
            'organisation_id': organisation_id,
            'full_sync': full_sync,
            'scheduled_at': (datetime.utcnow() + timedelta(seconds=delay_seconds)).isoformat()
        }

        # Store in Redis with appropriate expiration
        job_id = f"github_sync:{user_id}:{int(time.time())}"
        self.redis_service.set(
            job_id,
            json.dumps(job_data),
            ex=86400  # 24 hour expiration
        )

        # Add to sorted set for processing
        score = time.time() + delay_seconds
        self.redis_service.zadd("github_sync_queue", {job_id: score})

        return job_id

    def _cancel_scheduled_syncs(self, user_id: str) -> None:
        """
        Cancel all scheduled syncs for a user following Google Drive patterns.

        Args:
            user_id: The ID of the user
        """
        # Find all sync jobs for this user
        pattern = f"github_sync:{user_id}:*"
        keys = self.redis_service.keys(pattern)

        # Remove from sorted set and delete keys
        for key in keys:
            self.redis_service.zrem("github_sync_queue", key)
            self.redis_service.delete(key)

    def _cancel_scheduled_syncs_for_organization(self, organisation_id: str) -> None:
        """
        Cancel all scheduled syncs for an organization following Google Drive patterns.

        Args:
            organisation_id: The ID of the organization
        """
        # Find all GitHub sync jobs
        pattern = f"github_sync:*"
        keys = self.redis_service.keys(pattern)

        cancelled_count = 0
        # Check each job to see if it belongs to this organization
        for key in keys:
            try:
                job_data_str = self.redis_service.get(key)
                if job_data_str:
                    job_data = json.loads(job_data_str)
                    if job_data.get('organisation_id') == organisation_id:
                        self.redis_service.zrem("github_sync_queue", key)
                        self.redis_service.delete(key)
                        cancelled_count += 1
            except Exception as e:
                logger.warning(f"Error checking job {key}: {str(e)}")

        logger.info(f"Cancelled {cancelled_count} scheduled GitHub sync jobs for organization {organisation_id}")

    def sync_github(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int, int]:
        """
        Perform a full sync of GitHub data for an organisation using modular components.

        Args:
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, repositories_synced, total_items_synced)
        """
        try:
            # Validate organisation_id is provided
            if not organisation_id:
                logger.error("organisation_id is required for GitHub sync")
                return False, "organisation_id is required", 0, 0

            logger.info(f"Starting GitHub sync for organisation: {organisation_id}")

            # Note: GitHub Source node is already created in organization service when adding GitHub source

            # Initialize API client with credentials
            if not self._initialize_api_client(organisation_id):
                return False, "Failed to initialize GitHub API client", 0, 0

            # Use the new sync service for organization-level sync
            success, message, items_synced = self.sync_service.sync_organization(organisation_id, full_sync)

            if success:
                # Update last sync time
                self._update_last_sync_time(organisation_id)

                # For backward compatibility, estimate repositories synced
                repositories_synced = max(1, items_synced // 10)  # Rough estimate

                logger.info(f"GitHub sync completed: {repositories_synced} repositories, {items_synced} total items")
                return True, f"Successfully synced {repositories_synced} repositories", repositories_synced, items_synced
            else:
                return False, message, 0, 0

        except Exception as e:
            logger.error(f"Error in GitHub sync: {str(e)}")
            return False, f"Sync failed: {str(e)}", 0, 0

    def sync_github_user(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Perform user-level GitHub sync using modular components.

        Args:
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            # Validate organisation_id is provided
            if not organisation_id:
                logger.error("organisation_id is required for GitHub user sync")
                return False, "organisation_id is required", 0

            logger.info(f"Starting GitHub user sync for organisation: {organisation_id}")

            # Initialize API client with credentials
            if not self._initialize_api_client(organisation_id):
                return False, "Failed to initialize GitHub API client", 0

            # Use the new sync service for user-level sync
            success, message, items_synced = self.sync_service.sync_user(organisation_id, full_sync)

            if success:
                # Update last sync time
                self._update_last_sync_time(organisation_id)

                logger.info(f"GitHub user sync completed: {items_synced} items")
                return True, message, items_synced
            else:
                return False, message, 0

        except Exception as e:
            logger.error(f"Error in GitHub user sync: {str(e)}")
            return False, f"User sync failed: {str(e)}", 0

    def sync_repository_by_url(self, github_url: str, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Sync a specific GitHub repository by URL using modular components.

        Args:
            github_url: GitHub repository URL
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            # Extract repository info from URL
            repo_info = self.extract_repo_info_from_url(github_url)
            if not repo_info:
                return False, f"Invalid GitHub URL: {github_url}", 0

            owner = repo_info['owner']
            repo_name = repo_info['repo']

            logger.info(f"Syncing GitHub repository by URL: {owner}/{repo_name}")

            # Initialize API client with credentials
            if not self._initialize_api_client(organisation_id):
                return False, "Failed to initialize GitHub API client", 0

            # Get repository data using the new API client
            repo_data = self.api_client.get_repository(owner, repo_name)
            if not repo_data:
                return False, f"Failed to fetch repository data for {owner}/{repo_name}", 0

            # Use the new sync service to sync the repository
            items_synced = self.sync_service.sync_repository(repo_data, organisation_id, full_sync)

            if items_synced > 0:
                logger.info(f"Successfully synced GitHub repository: {owner}/{repo_name}, items: {items_synced}")
                return True, f"Successfully synced repository {owner}/{repo_name}", items_synced
            else:
                return False, f"Failed to sync repository {owner}/{repo_name}", 0

        except Exception as e:
            logger.error(f"Error syncing repository by URL: {str(e)}")
            return False, f"Repository sync failed: {str(e)}", 0



    def _update_last_sync_time(self, organisation_id: str):
        """Update last sync time following Google Drive patterns."""
        try:
            source_queries = SourceQueries()
            params = {
                'organisation_id': organisation_id,
                'source_type': SourceType.GITHUB.value,
                'last_sync_at': datetime.utcnow().isoformat()
            }
            execute_write_query(source_queries.UPDATE_SOURCE_LAST_SYNC, params)
            logger.debug(f"Updated last sync time for organisation {organisation_id}")
        except Exception as e:
            logger.error(f"Error updating last sync time: {str(e)}")

    def search_github_content(self, query: str, organisation_id: str, limit: int = 10) -> List[Dict]:
        """
        Search GitHub content using hybrid search engine.

        Args:
            query: Search query
            organisation_id: Organisation ID
            limit: Maximum number of results

        Returns:
            List of search results
        """
        try:
            # Validate required parameters
            if not organisation_id:
                logger.error("organisation_id is required for searching GitHub content")
                return []

            if not query or not query.strip():
                logger.error("query is required for searching GitHub content")
                return []

            if limit <= 0:
                logger.warning("Invalid limit provided, using default limit of 10")
                limit = 10

            # Use hybrid search engine for GitHub content
            search_engine = HybridSearchEngine()

            # Define GitHub entity types for search
            entity_types = [
                'GitHubRepository',
                'GitHubIssue',
                'GitHubPullRequest',
                'GitHubCodeFile'
            ]

            results = search_engine.search(
                query=query,
                organisation_id=organisation_id,
                entity_types=entity_types,
                limit=limit
            )

            return results

        except Exception as e:
            logger.error(f"Error searching GitHub content: {str(e)}")
            return []

    def get_sync_statistics(self, organisation_id: str) -> Dict[str, Any]:
        """
        Get sync statistics for an organisation following Google Drive patterns.

        Args:
            organisation_id: Organisation ID

        Returns:
            Dictionary containing sync statistics
        """
        try:
            # Validate required parameters
            if not organisation_id:
                logger.error("organisation_id is required for getting sync statistics")
                return {}

            # Get statistics from various GitHub entities
            stats = {
                'organisation_id': organisation_id,
                'last_updated': datetime.utcnow().isoformat(),
                'entities': {}
            }

            # Get counts for different entity types
            entity_queries = [
                ('users', self.user_queries.COUNT_USERS_BY_ORGANISATION),
                ('organizations', self.organization_queries.COUNT_ORGANIZATIONS_BY_ORGANISATION),
                ('repositories', self.repository_queries.COUNT_REPOSITORIES_BY_ORGANISATION),
                ('issues', self.issue_queries.COUNT_ISSUES_BY_ORGANISATION),
                ('pull_requests', self.pull_request_queries.COUNT_PULL_REQUESTS_BY_ORGANISATION),
                ('commits', self.commit_queries.COUNT_COMMITS_BY_ORGANISATION),
                ('files', self.file_queries.COUNT_FILES_BY_ORGANISATION),
                ('teams', self.team_queries.COUNT_TEAMS_BY_ORGANISATION),
                ('tags', self.tag_queries.COUNT_TAGS_BY_ORGANISATION),
                ('releases', self.release_queries.COUNT_RELEASES_BY_ORGANISATION),
                ('comments', self.comment_queries.COUNT_COMMENTS_BY_ORGANISATION),
                ('reviews', self.review_queries.COUNT_REVIEWS_BY_ORGANISATION)
            ]

            for entity_type, query in entity_queries:
                try:
                    result = execute_read_query(query, {'organisation_id': organisation_id})
                    count = result[0]['count'] if result and 'count' in result[0] else 0
                    stats['entities'][entity_type] = count
                except Exception as e:
                    logger.warning(f"Error getting count for {entity_type}: {str(e)}")
                    stats['entities'][entity_type] = 0

            # Get last sync time from source
            try:
                source_queries = SourceQueries()
                source_result = execute_read_query(
                    source_queries.GET_SOURCE_BY_TYPE_AND_ORGANISATION,
                    {'organisation_id': organisation_id, 'source_type': SourceType.GITHUB.value}
                )
                if source_result:
                    stats['last_sync_time'] = source_result[0].get('last_sync_at')
                else:
                    stats['last_sync_time'] = None
            except Exception as e:
                logger.warning(f"Error getting last sync time: {str(e)}")
                stats['last_sync_time'] = None

            return stats

        except Exception as e:
            logger.error(f"Error getting GitHub sync statistics: {str(e)}")
            return {
                'organisation_id': organisation_id,
                'error': str(e),
                'last_updated': datetime.utcnow().isoformat()
            }

    def extract_repo_info_from_url(self, github_url: str) -> Optional[Dict[str, str]]:
        """
        Extract repository owner and name from a GitHub URL.

        Args:
            github_url: The GitHub URL

        Returns:
            Dictionary with owner and repo name or None if extraction failed
        """
        try:
            # Handle different URL formats
            # Format 1: https://github.com/owner/repo
            # Format 2: https://github.com/owner/repo/issues/123
            # Format 3: https://github.com/owner/repo/pull/456

            if "github.com/" in github_url:
                # Extract owner/repo from URL
                parts = github_url.split("github.com/")[1].split("/")
                if len(parts) >= 2:
                    owner = parts[0]
                    repo = parts[1]
                    return {"owner": owner, "repo": repo}

            logger.error(f"Unsupported GitHub URL format: {github_url}")
            return None

        except Exception as e:
            logger.error(f"Error extracting repo info from URL: {str(e)}")
            return None
